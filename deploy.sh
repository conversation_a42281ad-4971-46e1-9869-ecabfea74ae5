#!/bin/bash

# IQSF MVP Deployment Script for Google Cloud Run
# This script deploys the IQSF API to Google Cloud Run

set -e  # Exit on any error

# Configuration
PROJECT_ID="iqsf-466106"
SERVICE_NAME="iqsf-api"
REGION="us-central1"
IMAGE_NAME="gcr.io/${PROJECT_ID}/${SERVICE_NAME}"

echo "🚀 Starting IQSF MVP deployment to Cloud Run..."
echo "Project ID: ${PROJECT_ID}"
echo "Service Name: ${SERVICE_NAME}"
echo "Region: ${REGION}"

# Check if gcloud is installed and authenticated
if ! command -v gcloud &> /dev/null; then
    echo "❌ Error: gcloud CLI is not installed. Please install it first."
    exit 1
fi

# Set the project
echo "📋 Setting Google Cloud project..."
gcloud config set project ${PROJECT_ID}

# Enable required APIs
echo "🔧 Enabling required Google Cloud APIs..."
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable containerregistry.googleapis.com

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "❌ Error: .env file not found. Please create one based on .env.example"
    echo "Required variables: PINECONE_API_KEY, ADMIN_API_KEY"
    exit 1
fi

# Load environment variables
echo "📝 Loading environment variables..."
source .env

# Validate required environment variables
if [ -z "$PINECONE_API_KEY" ]; then
    echo "❌ Error: PINECONE_API_KEY is not set in .env file"
    exit 1
fi

if [ -z "$ADMIN_API_KEY" ]; then
    echo "❌ Error: ADMIN_API_KEY is not set in .env file"
    exit 1
fi

# Create secrets in Google Secret Manager
echo "🔐 Creating secrets in Google Secret Manager..."
gcloud services enable secretmanager.googleapis.com

# Create or update PINECONE_API_KEY secret
echo -n "$PINECONE_API_KEY" | gcloud secrets create pinecone-api-key --data-file=- --replication-policy=automatic || \
echo -n "$PINECONE_API_KEY" | gcloud secrets versions add pinecone-api-key --data-file=-

# Create or update ADMIN_API_KEY secret
echo -n "$ADMIN_API_KEY" | gcloud secrets create admin-api-key --data-file=- --replication-policy=automatic || \
echo -n "$ADMIN_API_KEY" | gcloud secrets versions add admin-api-key --data-file=-

# Build and deploy using Cloud Build
echo "🏗️  Building and deploying with Cloud Build..."
gcloud builds submit --config cloudbuild.yaml \
    --substitutions=_SERVICE_NAME=${SERVICE_NAME},_REGION=${REGION}

# Deploy with secrets
echo "🚀 Deploying to Cloud Run with secrets..."
gcloud run deploy ${SERVICE_NAME} \
    --image ${IMAGE_NAME}:latest \
    --region ${REGION} \
    --platform managed \
    --allow-unauthenticated \
    --memory 4Gi \
    --cpu 2 \
    --timeout 900 \
    --concurrency 10 \
    --max-instances 5 \
    --set-env-vars TRANSFORMERS_CACHE=/app/model_cache \
    --set-secrets PINECONE_API_KEY=pinecone-api-key:latest,ADMIN_API_KEY=admin-api-key:latest

# Get the service URL
SERVICE_URL=$(gcloud run services describe ${SERVICE_NAME} --region=${REGION} --format='value(status.url)')

echo ""
echo "✅ Deployment completed successfully!"
echo "🌐 Service URL: ${SERVICE_URL}"
echo ""
echo "Test your API:"
echo "curl ${SERVICE_URL}/"
echo "curl \"${SERVICE_URL}/v1/qsi?location=Texas,USA\""
echo ""
echo "📚 API Documentation: ${SERVICE_URL}/docs"
